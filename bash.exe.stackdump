Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA0E580000 ntdll.dll
7FFA0D6D0000 KERNEL32.DLL
7FFA0B800000 KERNELBASE.dll
7FFA0CC10000 USER32.dll
7FFA0BF30000 win32u.dll
7FFA0D670000 GDI32.dll
000210040000 msys-2.0.dll
7FFA0BD40000 gdi32full.dll
7FFA0BE80000 msvcp_win.dll
7FFA0BBF0000 ucrtbase.dll
7FFA0C860000 advapi32.dll
7FFA0D480000 msvcrt.dll
7FFA0C600000 sechost.dll
7FFA0C6B0000 RPCRT4.dll
7FFA0ADF0000 CRYPTBASE.DLL
7FFA0B6D0000 bcryptPrimitives.dll
7FFA0C3D0000 IMM32.DLL
